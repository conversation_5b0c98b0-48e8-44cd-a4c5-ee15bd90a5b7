import 'package:bloodplus/core/constants/app_colors.dart';
import 'package:bloodplus/core/language_helper/localization.dart';
import 'package:bloodplus/data/manager/app_state_notifier.dart';
import 'package:bloodplus/data/manager/user_manager.dart';
import 'package:bloodplus/presentation/features/schedule/donation_event_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';

class DaysWaitingScreen extends StatefulWidget {
  const DaysWaitingScreen({Key? key}) : super(key: key);

  @override
  State<DaysWaitingScreen> createState() => _DaysWaitingScreenState();
}

class _DaysWaitingScreenState extends State<DaysWaitingScreen>
    with TickerProviderStateMixin {
  final UserManager _userManager = UserManager();
  late AnimationController _animationController;
  late AnimationController _heartBeatController;
  late AnimationController _fillController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _slideAnimation;
  late Animation<double> _heartBeatAnimation;
  late Animation<double> _fillAnimation;

  @override
  void initState() {
    super.initState();

    // Main animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Heart beat animation for ready donations
    _heartBeatController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    )..repeat(reverse: true);

    // Fill animation controller
    _fillController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<double>(begin: 50.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 0.8, curve: Curves.easeOutCubic),
      ),
    );

    _heartBeatAnimation = Tween<double>(begin: 1.0, end: 1.15).animate(
      CurvedAnimation(parent: _heartBeatController, curve: Curves.elasticOut),
    );

    _fillAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fillController, curve: Curves.easeInOutQuart),
    );

    _loadDaysWaiting();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _heartBeatController.dispose();
    _fillController.dispose();
    super.dispose();
  }

  Future<void> _loadDaysWaiting() async {
    final userId = await _userManager.getUserId();
    final token = await _userManager.getUserToken();
    if (userId != null && token != null) {
      await Provider.of<AppStateNotifier>(
        context,
        listen: false,
      ).fetchDaysWaiting(userId, token);
      _animationController.forward();
      _fillController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppStateNotifier>(context);
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xffff1b1b),
              Color(0xfff14d4d),
              Color(0xffff4ba7),
              Color(0xffff71f1),
            ],
            stops: [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: SafeArea(
          child: CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              _buildSliverAppBar(localizations),
              SliverToBoxAdapter(
                child: RefreshIndicator(
                  onRefresh: _loadDaysWaiting,
                  color: Colors.white,
                  backgroundColor: AppColors.primaryRed,
                  child:
                      appState.isLoading
                          ? _buildLoadingState()
                          : appState.daysWaiting == null
                          ? _buildEmptyState(localizations)
                          : _buildContent(
                            context,
                            appState.daysWaiting!,
                            localizations,
                          ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSliverAppBar(AppLocalizations localizations) {
    return SliverAppBar(
      expandedHeight: 220,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Color.fromRGBO(255, 255, 255, 0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Color.fromRGBO(255, 255, 255, 0.3)),
        ),
        child: IconButton(
          icon: const Icon(Icons.arrow_back_ios_rounded, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
          decoration: BoxDecoration(
            color: Color.fromRGBO(255, 255, 255, 0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Color.fromRGBO(255, 255, 255, 0.3)),
          ),
          child: IconButton(
            icon: const Icon(Icons.refresh_rounded, color: Colors.white),
            onPressed: _loadDaysWaiting,
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xffea6666),
                Color(0xffa24b5b),
                Color(0xfffb9393),
                Color(0xFFf5576c),
              ],
              stops: [0.0, 0.3, 0.7, 1.0],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(24, 80, 24, 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(255, 255, 255, 0.25),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: Color.fromRGBO(255, 255, 255, 0.4),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Color.fromRGBO(0, 0, 0, 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(3),
                        decoration: BoxDecoration(
                          color: Color.fromRGBO(255, 255, 255, 0.3),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.favorite_rounded,
                          color: Colors.red,
                          size: 9,
                        ),
                      ),
                      const SizedBox(width: 10),
                      Text(
                        localizations.translate('donation_status'),
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 5),
                Text(
                  localizations.translate('donation_waiting_periods'),
                  style: GoogleFonts.poppins(
                    fontSize: 28,
                    fontWeight: FontWeight.w800,
                    color: Colors.white,
                    height: 1.1,
                    shadows: [
                      Shadow(
                        color: Color.fromRGBO(0, 0, 0, 0.2),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Text(
                  localizations.translate(
                    'donation_waiting_periods_description',
                  ),
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    color: Color.fromRGBO(255, 255, 255, 0.9),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Color.fromRGBO(255, 255, 255, 0.9),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Color.fromRGBO(0, 0, 0, 0.1),
                    blurRadius: 20,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFf5576c)),
                strokeWidth: 3,
              ),
            ),
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: Color.fromRGBO(255, 255, 255, 0.9),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                'Đang tải thông tin hiến máu...',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  color: const Color(0xFF4A5568),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(AppLocalizations localizations) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(28),
              decoration: BoxDecoration(
                color: Color.fromRGBO(255, 255, 255, 0.9),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Color.fromRGBO(0, 0, 0, 0.1),
                    blurRadius: 20,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Icon(
                Icons.info_outline_rounded,
                size: 52,
                color: Colors.grey[400],
              ),
            ),
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              decoration: BoxDecoration(
                color: Color.fromRGBO(255, 255, 255, 0.9),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Column(
                children: [
                  Text(
                    localizations.translate('no_data_available'),
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      color: const Color(0xFF2D3748),
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    'Không tìm thấy dữ liệu hiến máu',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    Map<String, int> daysWaiting,
    AppLocalizations localizations,
  ) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(20, 20, 20, 100),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInfoCard(context, localizations),
                  const SizedBox(height: 24),
                  _buildDaysWaitingList(context, daysWaiting, localizations),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoCard(BuildContext context, AppLocalizations localizations) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Color.fromRGBO(255, 255, 255, 0.95),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(0, 0, 0, 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFFf093fb), Color(0xFFf5576c)],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Color.fromRGBO(245, 87, 108, 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.info_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    localizations.translate('donation_info'),
                    style: GoogleFonts.poppins(
                      fontSize: 22,
                      fontWeight: FontWeight.w800,
                      color: const Color(0xFF1A202C),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 18),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(18),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFFF7FAFC), Color(0xFFEDF2F7)],
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: const Color(0xFFE2E8F0), width: 1),
              ),
              child: Text(
                localizations.translate('waiting_periods_info'),
                style: GoogleFonts.poppins(
                  fontSize: 15,
                  color: const Color(0xFF4A5568),
                  height: 1.6,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDaysWaitingList(
    BuildContext context,
    Map<String, int> daysWaiting,
    AppLocalizations localizations,
  ) {
    final Map<String, Map<String, dynamic>> donationTypes = {
      'wholeBloodDaysLeft': {
        'name': localizations.translate('whole_blood'),
        'icon': Icons.water_drop_rounded,
        'colors': [const Color(0xFFDC2626), const Color(0xFFEF4444)],
        'totalDays': 84,
      },
      'redBloodCellsDaysLeft': {
        'name': localizations.translate('red_blood_cells'),
        'icon': Icons.bloodtype_rounded,
        'colors': [const Color(0xFFE11D48), const Color(0xFFF43F5E)],
        'totalDays': 84,
      },
      'plasmaDaysLeft': {
        'name': localizations.translate('plasma'),
        'icon': Icons.opacity_rounded,
        'colors': [const Color(0xFFF59E0B), const Color(0xFFFBBF24)],
        'totalDays': 14,
      },
      'plateletsDaysLeft': {
        'name': localizations.translate('platelets'),
        'icon': Icons.grain_rounded,
        'colors': [const Color(0xFF7C3AED), const Color(0xFF8B5CF6)],
        'totalDays': 14,
      },
      'whiteBloodCellsDaysLeft': {
        'name': localizations.translate('white_blood_cells'),
        'icon': Icons.circle_rounded,
        'colors': [const Color(0xFF0891B2), const Color(0xFF06B6D4)],
        'totalDays': 7,
      },
    };

    return Column(
      children:
          donationTypes.entries.toList().asMap().entries.map((entry) {
            final index = entry.key;
            final mapEntry = entry.value;
            final key = mapEntry.key;
            final daysLeft = daysWaiting[key] ?? 0;
            final typeInfo = mapEntry.value;

            return AnimatedContainer(
              duration: Duration(milliseconds: 400 + (index * 150)),
              margin: const EdgeInsets.only(bottom: 16),
              child: _buildDonationTypeCard(
                context,
                typeInfo,
                daysLeft,
                localizations,
              ),
            );
          }).toList(),
    );
  }

  Widget _buildDonationTypeCard(
    BuildContext context,
    Map<String, dynamic> typeInfo,
    int daysLeft,
    AppLocalizations localizations,
  ) {
    final canDonate = daysLeft <= 0;
    final totalDays = typeInfo['totalDays'] as int;
    final progress = canDonate ? 1.0 : (totalDays - daysLeft) / totalDays;

    return Container(
      decoration: BoxDecoration(
        color: Color.fromRGBO(255, 255, 255, 0.95),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(0, 0, 0, 0.08),
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
          if (canDonate)
            BoxShadow(
              color: Color.fromRGBO(0, 128, 0, 0.2),
              blurRadius: 20,
              offset: const Offset(0, 5),
            ),
        ],
        border: Border.all(
          color:
              canDonate
                  ? Color.fromRGBO(0, 128, 0, 0.4)
                  : Color.fromRGBO(128, 128, 128, 0.15),
          width: canDonate ? 2 : 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            // Animated Heart with Fill Effect
            GestureDetector(
              onTap:
                  canDonate
                      ? () {
                        // Điều hướng đến màn hình chi tiết khi nhấn vào trái tim và đã full
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const DonationEventScreen(),
                          ),
                        );
                      }
                      : null, // Vô hiệu hóa sự kiện khi không thể donate
              child: Container(
                width: 90,
                height: 90,
                child: AnimatedBuilder(
                  animation: Listenable.merge([
                    _fillController,
                    canDonate ? _heartBeatController : _animationController,
                  ]),
                  builder: (context, child) {
                    return Transform.scale(
                      scale: canDonate ? _heartBeatAnimation.value : 1.0,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // Heart container with glow effect
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: typeInfo['colors'][0].withOpacity(0.3),
                                  blurRadius: canDonate ? 20 : 12,
                                  spreadRadius: canDonate ? 4 : 2,
                                ),
                              ],
                            ),
                          ),
                          // Heart outline
                          Container(
                            width: 80,
                            height: 80,
                            child: CustomPaint(
                              painter: HeartPainter(
                                fillProgress: progress * _fillAnimation.value,
                                colors: typeInfo['colors'],
                                isReady: canDonate,
                              ),
                            ),
                          ),
                          // Progress text with animated appearance
                          AnimatedOpacity(
                            opacity: _fillAnimation.value,
                            duration: const Duration(milliseconds: 500),
                            child: Text(
                              canDonate
                                  ? '💖'
                                  : '${(progress * 100).toStringAsFixed(0)}%',
                              style: GoogleFonts.poppins(
                                fontSize: canDonate ? 30 : 19,
                                fontWeight: FontWeight.w600,
                                color: canDonate ? Colors.white : Colors.white,
                                shadows: [
                                  Shadow(
                                    color: Color.fromRGBO(0, 0, 0, 0.5),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    typeInfo['name'],
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      color: const Color(0xFF1A202C),
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    canDonate
                        ? localizations.translate('ready_to_donate')
                        : localizations.translate('waiting_periods'),
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: const Color(0xFF718096),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (canDonate) const SizedBox(height: 8),
                  if (canDonate)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF10B981), Color(0xFF34D399)],
                        ),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Color.fromRGBO(0, 128, 0, 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Text(
                        localizations.translate('can_donate_today'),
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.white,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ),
                ],
              ),
            ),
            if (!canDonate)
              _buildStatusIndicator(context, daysLeft, localizations),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(
    BuildContext context,
    int daysLeft,
    AppLocalizations localizations,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFEF4444), Color(0xFFF87171)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(255, 0, 0, 0.3),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.schedule_rounded, color: Colors.white, size: 16),
          const SizedBox(width: 6),
          Text(
            '$daysLeft ${localizations.translate('days')}',
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontWeight: FontWeight.w700,
              fontSize: 13,
            ),
          ),
        ],
      ),
    );
  }
}

// Custom Heart Painter with animated fill effect
class HeartPainter extends CustomPainter {
  final double fillProgress;
  final List<Color> colors;
  final bool isReady;

  HeartPainter({
    required this.fillProgress,
    required this.colors,
    required this.isReady,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..style = PaintingStyle.fill
          ..strokeWidth = 2;

    final path = _createHeartPath(size);

    // Draw heart outline
    paint.style = PaintingStyle.stroke;
    paint.color = colors[0].withOpacity(0.3);
    paint.strokeWidth = 2;
    canvas.drawPath(path, paint);

    // Draw filled portion based on progress
    paint.style = PaintingStyle.fill;

    if (fillProgress > 0) {
      final gradient = LinearGradient(
        begin: Alignment.bottomCenter,
        end: Alignment.topCenter,
        colors: colors,
        stops: const [0.0, 1.0],
      );

      final rect = Rect.fromLTWH(0, 0, size.width, size.height);
      paint.shader = gradient.createShader(rect);

      // Create clipped path for fill animation
      canvas.save();

      // Calculate fill height based on progress
      final fillHeight = size.height * (1 - fillProgress);
      final clipRect = Rect.fromLTWH(
        0,
        fillHeight,
        size.width,
        size.height - fillHeight,
      );

      canvas.clipRect(clipRect);
      canvas.drawPath(path, paint);
      canvas.restore();
    }

    // Add extra glow for ready state
    if (isReady) {
      paint.style = PaintingStyle.stroke;
      paint.strokeWidth = 3;
      paint.color = colors[1].withOpacity(0.6);
      canvas.drawPath(path, paint);
    }
  }

  Path _createHeartPath(Size size) {
    final path = Path();
    final width = size.width;
    final height = size.height;

    // Heart shape path
    path.moveTo(width * 0.5, height * 0.25);

    // Left curve
    path.cubicTo(
      width * 0.2,
      height * 0.1,
      width * 0.1,
      height * 0.4,
      width * 0.2,
      height * 0.6,
    );

    // Bottom left curve
    path.cubicTo(
      width * 0.35,
      height * 0.8,
      width * 0.5,
      height * 0.9,
      width * 0.5,
      height * 0.9,
    );

    // Bottom right curve
    path.cubicTo(
      width * 0.5,
      height * 0.9,
      width * 0.65,
      height * 0.8,
      width * 0.8,
      height * 0.6,
    );

    // Right curve
    path.cubicTo(
      width * 0.9,
      height * 0.4,
      width * 0.8,
      height * 0.1,
      width * 0.5,
      height * 0.25,
    );

    path.close();
    return path;
  }

  @override
  bool shouldRepaint(covariant HeartPainter oldDelegate) {
    return oldDelegate.fillProgress != fillProgress ||
        oldDelegate.isReady != isReady;
  }
}
