import 'dart:convert';
import 'package:bloodplus/data/manager/days_waiting_manager.dart';
import 'package:bloodplus/data/models/user_model.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:shared_preferences/shared_preferences.dart';



class UserManager {
  static const String _tokenKey = 'auth_token';
  static const String _userIdKey = 'user_id';
  static const String _userInfoKey = 'user_info';

  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: ['email', 'profile'],
    serverClientId:
    '961539697971-01v10pf7gd2e23j2hb1man5s758sh795.apps.googleusercontent.com',
  );

  Future<void> saveUserToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }

  Future<String?> getUserToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  Future<void> saveUserId(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userIdKey, userId);
  }

  Future<String?> getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userIdKey);
  }

  Future<void> saveUserInfo(String userId, dynamic user) async {
    final prefs = await SharedPreferences.getInstance();
    String userJson;

    if (user is UserModel) {
      userJson = jsonEncode(user.toJson());
    } else if (user is Map<String, dynamic>) {
      userJson = jsonEncode(user);
    } else {
      throw Exception('Invalid user data type');
    }

    await prefs.setString('$_userInfoKey$userId', userJson);
  }

  Future<UserModel?> getUserInfo(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = prefs.getString('$_userInfoKey$userId');
    if (userJson != null) {
      try {
        return UserModel.fromJson(jsonDecode(userJson));
      } catch (e) {
        // Handle error silently
      }
    }
    return null;
  }

  Future<void> clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final userId = await getUserId();

    if (userId != null) {
      await prefs.remove('$_userInfoKey$userId');
    }

    // Xóa token và userId
    await prefs.remove(_tokenKey);
    await prefs.remove(_userIdKey);

    // Xóa các thông tin profile đã lưu
    await prefs.remove('user_name');
    await prefs.remove('user_email');
    await prefs.remove('donation_count');

    await DaysWaitingManager().clearDaysWaiting();
    await _googleSignIn.signOut();
  }

  Future<void> updateUserProfile(UserModel userProfile) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_name', userProfile.name ?? '');
    await prefs.setString('user_email', userProfile.email ?? '');
    await prefs.setInt('donation_count', userProfile.donationCount ?? 0);
    // Lưu các thông tin khác của người dùng nếu cần
  }

}